/*------------------------------------------------------------
@Author:         <PERSON> (Accenture) May 11, 2020
@Description:    Test class for the ContactDetailsSectionController
----------------------------------------------------------- */
@isTest(isParallel = true)
public class ContactDetailsSectionController_Test {
    //Author: <PERSON>
    //Test method for getEmailWebServiceInterface and getMobileWebServiceInterface
    static testMethod void getEmailWebServiceInterfaceTest(){
        UserDAOMock userDAOMock = (UserDAOMock) DAOFactory.getDAO(User.sObjectType);
        User ccUser = TestDataBuilder.buildTestUserWithRoleProfileVR(1, null, UtilityClass.profileIdMap.get(ConstantsProfiles.PROFILE_NAME_INTERNAL_CONTACT_CENTRE));     
        userDAOMock.insertRecords(new List<User>{ccUser}, false);
        
        Map<String, Object> getEmailWSIResponseMap = new Map<String, Object>();
        Map<String, Object> getMobileWSIResponseMap = new Map<String, Object>();
        List<Web_Service_Interface__c> wsiList = new List<Web_Service_Interface__c>();
        Test.startTest();
		Web_Service_Interface__c experianEmailValidator = TestDataBuilder.buildWebServiceInterface(ConstantsExperian.EXPERIAN_WSI_EMAIL_VALIDATOR, null, null, null);
        wsiList.add(experianEmailValidator);
		Web_Service_Interface__c experianMobileValidator = TestDataBuilder.buildWebServiceInterface(ConstantsExperian.EXPERIAN_WSI_MOBILE_VALIDATOR, null, null, null);
        wsiList.add(experianMobileValidator);
        insert wsiList;
        
        userDAOMock.runAsUser(ccUser);
        getEmailWSIResponseMap = ContactDetailsSectionController.getEmailWebServiceInterface();
        getMobileWSIResponseMap = ContactDetailsSectionController.getMobileWebServiceInterface();
        //getUserRecordAccess
        Boolean userHasAccess = ContactDetailsSectionController.getUserRecordAccess(ccUser.Id, null);
        Test.stopTest();
        
        //Assertions
        System.assertEquals(getEmailWSIResponseMap.get('url'), experianEmailValidator.Endpoint__c);
        System.assertEquals(getEmailWSIResponseMap.get('authToken'), experianEmailValidator.Authorization_Token__c);
        System.assertEquals(getEmailWSIResponseMap.get('timeout'), experianEmailValidator.Request_Timeout__c/1000); //converted to seconds
        System.assertEquals(getMobileWSIResponseMap.get('url'), experianMobileValidator.Endpoint__c);
        System.assertEquals(getMobileWSIResponseMap.get('authToken'), experianMobileValidator.Authorization_Token__c);
        System.assertEquals(getMobileWSIResponseMap.get('timeout'), experianMobileValidator.Request_Timeout__c/1000); //converted to seconds
        System.assertEquals(false, userHasAccess);
    }
    
    //test method for updateVerificationDetailsForIdCheck and getVerifiationDetailsForIdCheck (org account)
    static testmethod void updateVerificationDetailsForIdCheckOrgTest(){
		AccountDAOMock accountDAOMock = (AccountDAOMock) DAOFactory.getDAO(Account.sObjectType);   
        List<Account> orgAccList = new List<Account>();
		Account orgAccount = TestDataBuilder.buildTestAccount(1,  ConstantsGlobal.ACCOUNT_RT_ORGANISATION_ID);
        orgAccount.Email_Validation_Timestamp__c = Datetime.now();
        orgAccount.Mobile_Phone_Validation_Timestamp__c = Datetime.now();
        orgAccList.add(orgAccount);
		Account orgAccount2 = TestDataBuilder.buildTestAccount(1,  ConstantsGlobal.ACCOUNT_RT_ORGANISATION_ID);
        orgAccList.add(orgAccount2);
		accountDAOMock.insertRecords(orgAccList, false);
        
        ContactDetailsSectionController.ExperianVerificationDetails expDetails = new ContactDetailsSectionController.ExperianVerificationDetails();
        Test.startTest();
        accountDAOMock.throwQueryException = true;
        ContactDetailsSectionController.updateVerificationDetailsForIdCheck(orgAccList[0].Id, 'Successfully verified as deliverable', 'Verified', true);
        ContactDetailsSectionController.getVerificationDetailsForIdCheck(orgAccList[0].Id);
        
        accountDAOMock.throwQueryException = false;
        ContactDetailsSectionController.updateVerificationDetailsForIdCheck(orgAccList[0].Id, 'Successfully verified as deliverable', 'Verified', true);
        ContactDetailsSectionController.updateVerificationDetailsForIdCheck(orgAccList[1].Id, 'Successfully verified as deliverable', 'Verified', false);  
        expDetails = ContactDetailsSectionController.getVerificationDetailsForIdCheck(orgAccList[0].Id);                                                        
        Test.stopTest();
        
        System.assertEquals(orgAccList[0].Email_Validation_Message__c , 'Successfully verified as deliverable');
        System.assertEquals(orgAccList[0].Email_Validation_Status__c, 'Verified');
        System.assertEquals(orgAccList[1].Mobile_Phone_Validation_Message__c , 'Successfully verified as deliverable');
        System.assertEquals(orgAccList[1].Mobile_Phone_Validation_Status__c, 'Verified');
        System.assertEquals(true, expDetails != null);
    }
    
    //test method for updateVerificationDetailsForIdCheck, getVerificationDetailsForIdCheck (indiv account) and getContactDetails
    static testmethod void updateVerificationDetailsForIdCheckIndivTest(){
		AccountDAOMock accountDAOMock = (AccountDAOMock) DAOFactory.getDAO(Account.sObjectType);        
		ContactDAOMock contactDAOMock = (ContactDAOMock) DAOFactory.getDAO(Contact.sObjectType);
        Map<String, List<Sobject>> adviceHierarchyData = TestDataBuilderDAOMock.buildSinglePracticeHierarchy();
		List<Account> indivAccList = TestDataBuilderDAOMock.createIndividual(2, 'Golden', (Account)adviceHierarchyData.get('practices')[0], null);
        
        
        ContactDetailsSectionController.ExperianVerificationDetails expDetails = new ContactDetailsSectionController.ExperianVerificationDetails();
        Test.startTest();
        List<Contact> conList = contactDAOMock.getContactsByAccountIds(new Set<Id>{indivAccList[0].Id});
        for(Contact con: conList){
            con.Email_Validation_Message__c = 'Succesfully verified email as deliverable';
            con.Email_Validation_Status__c = 'Verified';
            con.Email_Validation_Timestamp__c = Datetime.now();
            con.Mobile_Phone_Validation_Message__c = 'Succesfully verified email as deliverable';
            con.Mobile_Phone_Validation_Status__c = 'Verified';
            con.Mobile_Phone_Validation_Timestamp__c = Datetime.now();
        }
        contactDAOMock.updateRecords(conList, false);
        
        //getVerificationDetailsForIdCheck
        expDetails = ContactDetailsSectionController.getVerificationDetailsForIdCheck(indivAccList[0].Id);
        
        //updateVerificationDetailsForIdCheck
        contactDAOMock.throwQueryException = true;
        ContactDetailsSectionController.updateVerificationDetailsForIdCheck(indivAccList[0].Id, 'Successfully verified as deliverable', 'Verified', true); 
        contactDAOMock.throwQueryException = false;
        ContactDetailsSectionController.updateVerificationDetailsForIdCheck(indivAccList[0].Id, 'Successfully verified as deliverable', 'Verified', true);
        ContactDetailsSectionController.updateVerificationDetailsForIdCheck(indivAccList[1].Id, 'Successfully verified as deliverable', 'Verified', false);
        
        //getContactDetails
        accountDAOMock.throwQueryException = true;       
        String conIdError = ContactDetailsSectionController.getContactDetails(indivAccList[0].Id);
        accountDAOMock.throwQueryException = false;
        String conId = ContactDetailsSectionController.getContactDetails(indivAccList[0].Id);
        Test.stopTest();
        
        Set<Id> accIdSet = new Set<Id>();
        accIdSet.add(indivAccList[0].Id);
        accIdSet.add(indivAccList[1].Id);
        List<Contact> updatedConList = contactDAOMock.getContactsByAccountIds(accIdSet);
        for(Contact con: updatedConList){
            if(con.AccountId == indivAccList[0].Id){
        		System.assertEquals(con.Email_Validation_Message__c, 'Successfully verified as deliverable');
        		System.assertEquals(con.Email_Validation_Status__c, 'Verified');
            }else{
        		System.assertEquals(con.Mobile_Phone_Validation_Message__c, 'Successfully verified as deliverable');   
        		System.assertEquals(con.Mobile_Phone_Validation_Status__c, 'Verified');                
            }
        }
    }
    
    //Author: Heither Ann Ballero
    //Test method for calculateMonthsInBetween
    static testMethod void calculateMonthsInBetweenTest(){
        Integer numOfMonths;
        DateTime dt = System.now();
        Date dateToday = Date.newinstance(dt.year(), dt.month(), dt.day());
        Test.startTest();
        numOfMonths = ContactDetailsSectionController.calculateMonthsInBetween(dateToday);
        Test.stopTest();

        //Assertions
        System.assertEquals(numOfMonths, 0);
    }

    @isTest
    static void hasMobileChangedInPastMonths_ContactWithNoHistory_ReturnsFalse() {
        // Arrange
        Map<String, List<Sobject>> adviceHierarchyData = TestDataBuilderDAOMock.buildSinglePracticeHierarchy();
        List<Account> individualAccounts = TestDataBuilderDAOMock.createIndividual(1, ConstantsGlobal.ACCOUNT_PARTY_TYPE_SOURCE_PARTY, (Account)adviceHierarchyData.get('practices')[0], null);

        ContactDAOMock contactDAOMock = (ContactDAOMock) DAOFactory.getDAO(Contact.sObjectType);
        List<Contact> contacts = contactDAOMock.getRecords();
        Contact contactWithNoMobileHistory = contacts[0];
        Integer monthsToCheck = 3;

        // Act
        Test.startTest();
        Boolean hasChanged = ContactDetailsSectionController.hasMobileChangedInPastMonths(contactWithNoMobileHistory.Id, monthsToCheck);
        Test.stopTest();

        // Assert
        System.assert(!hasChanged, 'Expected no mobile changes when Contact has no mobile phone history');
    }
}